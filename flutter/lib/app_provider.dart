import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/core/services/cache_service.dart';

/// Notifier для управления состоянием приложения
class AppNotifier extends StateNotifier<AppState> {
  AppNotifier() : super(const AppState()) {
    _loadDrawerWidth();
  }

  /// Загружает сохраненную ширину drawer
  Future<void> _loadDrawerWidth() async {
    final savedWidth = CacheService.instance.getDrawerWidth();
    changeDrawerWidth(savedWidth);
  }

  /// Изменяет активный экран
  void changeActiveScreen(DrawerMode activeScreen) {
    state = state.copyWith(
      activeScreen: activeScreen,
      drawerMode: activeScreen,
    );
  }

  /// Изменяет режим боковой панели
  void changeDrawerMode(DrawerMode drawerMode) {
    state = state.copyWith(drawerMode: drawerMode, activeScreen: drawerMode);
  }

  /// Переходит на указанный экран
  void navigateToScreen(
    DrawerMode screen, {
    String? pageId,
    String? assistantId,
  }) {
    final newState = state.copyWith(activeScreen: screen, drawerMode: screen);

    switch (screen) {
      case DrawerMode.wiki:
        state = newState.copyWith(lastRequestedPageId: pageId);
        break;
      case DrawerMode.chat:
        state = newState.copyWith(lastRequestedAssistantId: assistantId);
        break;
      case DrawerMode.news:
        state = newState;
        break;
    }
  }

  /// Переходит на экран новостей
  void navigateToNews() {
    navigateToScreen(DrawerMode.news);
  }

  /// Переходит на экран Wiki
  void navigateToWiki(String pageId) {
    navigateToScreen(DrawerMode.wiki, pageId: pageId);
  }

  /// Переходит на экран AI
  void navigateToAI(String assistantId) {
    navigateToScreen(DrawerMode.chat, assistantId: assistantId);
  }

  /// Изменяет ширину drawer
  void changeDrawerWidth(double width) {
    state = state.copyWith(drawerWidth: width);
    // Сохраняем новую ширину в настройках
    CacheService.instance.saveDrawerWidth(width);
  }
}

/// Основной провайдер для состояния приложения
final appProvider = StateNotifierProvider<AppNotifier, AppState>((ref) {
  return AppNotifier();
});
