import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/features/drawer/app_drawer.dart';
import 'package:luxury_app/features/drawer/resizable_drawer.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/widgets/content_wrapper.dart';
import 'package:luxury_app/shared/widgets/global_audio_player.dart';

/// Оболочка приложения с Scaffold и боковой панелью
class AppShell extends ConsumerStatefulWidget {
  /// Основное содержимое приложения
  final Widget body;

  const AppShell({super.key, required this.body});

  @override
  ConsumerState<AppShell> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final bool showSideMenu = width >= AppSizes.mobileBreakpoint;

    // Всегда используем widget.body из роутера
    final Widget content = RepaintBoundary(
      child: ContentWrapper(
        contentKey: PageStorageKey('content-${widget.body.hashCode}'),
        child: widget.body,
      ),
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        final Widget mainLayout =
            !showSideMenu
                ? _buildMobileLayout(content)
                : Consumer(
                  builder: (context, ref, child) {
                    final state = ref.watch(appProvider);
                    return _buildDesktopLayout(constraints, content, state);
                  },
                );

        // Оборачиваем в Stack для отображения глобального аудиоплеера
        return Stack(children: [mainLayout, const GlobalAudioPlayer()]);
      },
    );
  }

  /// Построение мобильного макета
  Widget _buildMobileLayout(Widget content) {
    const double minWidth = 360;
    const double maxWidth = 900;

    return LayoutBuilder(
      builder: (context, constraints) {
        final double clampedWidth = constraints.maxWidth.clamp(
          minWidth,
          maxWidth,
        );

        return Scaffold(
          key: _scaffoldKey,
          drawer: AppDrawer(scaffoldKey: _scaffoldKey, roundedCorners: true),
          body: Align(
            alignment: Alignment.topCenter,
            child: SizedBox(width: clampedWidth, child: content),
          ),
        );
      },
    );
  }

  /// Построение десктопного макета
  Widget _buildDesktopLayout(
    BoxConstraints constraints,
    Widget content,
    AppState state,
  ) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Row(
        children: [
          // Левая панель
          ResizableDrawer(scaffoldKey: _scaffoldKey),
          // Основной контент
          Expanded(
            child: _buildDesktopContent(
              constraints,
              content,
              state.drawerWidth,
            ),
          ),
        ],
      ),
    );
  }

  /// Построение контента для desktop с адаптивной шириной
  Widget _buildDesktopContent(
    BoxConstraints constraints,
    Widget content,
    double drawerWidth,
  ) {
    const double minContentWidth = 600;
    const double maxContentWidth = 1200;

    final double availableWidth = constraints.maxWidth - drawerWidth;
    final double contentWidth = availableWidth.clamp(
      minContentWidth,
      maxContentWidth,
    );

    return Center(child: SizedBox(width: contentWidth, child: content));
  }
}
