import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/core/services/connectivity_service.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_repository.dart';
import 'package:luxury_app/features/news/news_repository.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_models.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_repository.dart';

/// Сервис для фоновой синхронизации всего контента
class BackgroundSyncService with LoggerMixin {
  static BackgroundSyncService? _instance;
  static BackgroundSyncService get instance =>
      _instance ??= BackgroundSyncService._();

  BackgroundSyncService._();

  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();

  StreamSubscription<bool>? _connectivitySubscription;
  bool _isInitialized = false;
  bool _isSyncing = false;

  WikiRepository? _wikiRepository;
  AIChatRepository? _chatRepository;
  NewsRepository? _newsRepository;

  /// Поток состояния синхронизации
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Текущий статус синхронизации
  SyncStatus get currentStatus =>
      _isSyncing ? SyncStatus.syncing : SyncStatus.idle;

  /// Инициализация сервиса
  Future<void> init({
    required WikiRepository wikiRepository,
    required AIChatRepository chatRepository,
    required NewsRepository newsRepository,
  }) async {
    if (_isInitialized) return;

    _wikiRepository = wikiRepository;
    _chatRepository = chatRepository;
    _newsRepository = newsRepository;

    // Подписываемся на изменения состояния подключения
    _connectivitySubscription = ConnectivityService
        .instance
        .supabaseStatusStream
        .listen((isConnected) {
          if (isConnected) {
            logInfo(
              '🌐 Подключение к интернету восстановлено - запускаем полную синхронизацию',
            );
            _performFullSync();
          } else {
            logInfo(
              '📱 Подключение к интернету потеряно - работаем в offline режиме',
            );
            _updateSyncStatus(SyncStatus.offline);
          }
        });

    // Если уже подключены - запускаем синхронизацию
    if (ConnectivityService.instance.isSupabaseConnected) {
      _performFullSync();
    } else {
      _updateSyncStatus(SyncStatus.offline);
    }

    _isInitialized = true;
    logInfo('✅ BackgroundSyncService инициализирован');
  }

  /// Выполняет полную синхронизацию всего контента
  Future<void> _performFullSync() async {
    if (_isSyncing) {
      logInfo('⏳ Синхронизация уже выполняется, пропускаем');
      return;
    }

    _isSyncing = true;
    _updateSyncStatus(SyncStatus.syncing);

    try {
      logInfo('🔄 Начинаем полную синхронизацию контента...');

      // Синхронизируем все данные параллельно
      await Future.wait([_syncWikiContent(), _syncChats(), _syncNews()]);

      logInfo('✅ Полная синхронизация завершена успешно');
      _updateSyncStatus(SyncStatus.completed);
    } catch (e) {
      logError('❌ Ошибка при полной синхронизации: $e');
      _updateSyncStatus(SyncStatus.error);
    } finally {
      _isSyncing = false;

      // Через 3 секунды возвращаем статус к idle
      Timer(const Duration(seconds: 3), () {
        _updateSyncStatus(SyncStatus.idle);
      });
    }
  }

  /// Синхронизирует весь контент Wiki
  Future<void> _syncWikiContent() async {
    if (_wikiRepository == null) return;

    try {
      logInfo('📚 Синхронизируем Wiki контент...');

      // Загружаем иерархию папок
      final folders = await _wikiRepository!.getFolderHierarchy(
        forceRefresh: true,
      );

      // Собираем все файлы из всех папок
      final allFiles = <WikiFile>[];
      _collectAllFiles(folders, allFiles);

      logInfo('📄 Найдено ${allFiles.length} файлов для предзагрузки');

      // Предзагружаем содержимое всех файлов
      final futures = allFiles.map((file) async {
        try {
          await _wikiRepository!.getFileContent(file.id, forceRefresh: true);
          if (kDebugMode) {
            log('✅ Предзагружен файл: ${file.name}');
          }
        } catch (e) {
          if (kDebugMode) {
            log('❌ Ошибка предзагрузки файла ${file.name}: $e');
          }
        }
      });

      // Ждем завершения всех загрузок (максимум 30 секунд)
      await Future.wait(futures).timeout(const Duration(seconds: 30));

      logInfo('✅ Wiki контент синхронизирован');
    } catch (e) {
      logError('❌ Ошибка синхронизации Wiki: $e');
    }
  }

  /// Рекурсивно собирает все файлы из иерархии папок
  void _collectAllFiles(List<WikiFolder> folders, List<WikiFile> allFiles) {
    for (final folder in folders) {
      allFiles.addAll(folder.files);
      _collectAllFiles(folder.subfolders, allFiles);
    }
  }

  /// Синхронизирует чаты
  Future<void> _syncChats() async {
    if (_chatRepository == null) return;

    try {
      logInfo('💬 Синхронизируем чаты...');

      // Загружаем список чатов
      final chats = await _chatRepository!.getChats();

      // Предзагружаем сообщения для каждого чата
      final futures = chats.map((chat) async {
        try {
          await _chatRepository!.getChatMessages(chat.id);
          if (kDebugMode) {
            log('✅ Предзагружены сообщения чата: ${chat.title}');
          }
        } catch (e) {
          if (kDebugMode) {
            log('❌ Ошибка предзагрузки чата ${chat.title}: $e');
          }
        }
      });

      // Ждем завершения всех загрузок (максимум 20 секунд)
      await Future.wait(futures).timeout(const Duration(seconds: 20));

      logInfo('✅ Чаты синхронизированы');
    } catch (e) {
      logError('❌ Ошибка синхронизации чатов: $e');
    }
  }

  /// Синхронизирует новости
  Future<void> _syncNews() async {
    if (_newsRepository == null) return;

    try {
      logInfo('📰 Синхронизируем новости...');

      await _newsRepository!.getNews(forceRefresh: true);

      logInfo('✅ Новости синхронизированы');
    } catch (e) {
      logError('❌ Ошибка синхронизации новостей: $e');
    }
  }

  /// Принудительная синхронизация (для вызова из UI)
  Future<void> forceSync() async {
    if (!ConnectivityService.instance.isSupabaseConnected) {
      logInfo('❌ Принудительная синхронизация невозможна - нет подключения');
      return;
    }

    logInfo('🔄 Запуск принудительной синхронизации...');
    await _performFullSync();
  }

  /// Обновляет статус синхронизации
  void _updateSyncStatus(SyncStatus status) {
    _syncStatusController.add(status);
  }

  /// Освобождение ресурсов
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncStatusController.close();
    _isInitialized = false;
  }
}

/// Статус синхронизации
enum SyncStatus {
  idle, // Ожидание
  syncing, // Синхронизация в процессе
  completed, // Синхронизация завершена
  error, // Ошибка синхронизации
  offline, // Offline режим
}

/// Расширение для удобного отображения статуса
extension SyncStatusExtension on SyncStatus {
  String get displayName {
    switch (this) {
      case SyncStatus.idle:
        return 'Готов';
      case SyncStatus.syncing:
        return 'Синхронизация...';
      case SyncStatus.completed:
        return 'Завершено';
      case SyncStatus.error:
        return 'Ошибка';
      case SyncStatus.offline:
        return 'Offline';
    }
  }
}
