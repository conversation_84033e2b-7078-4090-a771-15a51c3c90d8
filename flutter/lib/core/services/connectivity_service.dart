import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';

import 'supabase_service.dart';

/// Сервис для отслеживания состояния подключения к Supabase
class ConnectivityService {
  static ConnectivityService? _instance;
  static ConnectivityService get instance =>
      _instance ??= ConnectivityService._();

  ConnectivityService._();

  final StreamController<bool> _supabaseStatusController =
      StreamController<bool>.broadcast();

  bool _isSupabaseConnected = false;
  Timer? _supabaseCheckTimer;

  /// Поток состояния подключения к Supabase
  Stream<bool> get supabaseStatusStream => _supabaseStatusController.stream;

  /// Текущее состояние подключения к Supabase
  bool get isSupabaseConnected => _isSupabaseConnected;

  /// Инициализация сервиса
  Future<void> init() async {
    // Запускаем периодическую проверку Supabase
    _startSupabaseHealthCheck();

    if (kDebugMode) {
      log('✅ ConnectivityService инициализирован');
    }
  }

  /// Запускает периодическую проверку состояния Supabase
  void _startSupabaseHealthCheck() {
    _supabaseCheckTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkSupabaseConnection(),
    );

    // Первоначальная проверка
    _checkSupabaseConnection();
  }

  /// Проверяет подключение к Supabase
  Future<void> _checkSupabaseConnection() async {
    try {
      final supabaseService = SupabaseService.instance;

      // Делаем простой запрос для проверки подключения
      await supabaseService.client
          .from('chats')
          .select('id')
          .limit(1)
          .timeout(const Duration(seconds: 5));

      _updateSupabaseStatus(true);
    } catch (e) {
      _updateSupabaseStatus(false);

      if (kDebugMode) {
        log('❌ Supabase недоступен: $e');
      }
    }
  }

  /// Обновляет состояние подключения к Supabase
  void _updateSupabaseStatus(bool isConnected) {
    if (_isSupabaseConnected != isConnected) {
      _isSupabaseConnected = isConnected;
      _supabaseStatusController.add(isConnected);

      if (kDebugMode) {
        log('🔗 Supabase: ${isConnected ? 'подключен' : 'отключен'}');
      }
    }
  }

  /// Принудительная проверка подключения к Supabase
  Future<void> forceCheckSupabaseConnection() async {
    await _checkSupabaseConnection();
  }

  /// Освобождение ресурсов
  void dispose() {
    _supabaseCheckTimer?.cancel();
    _supabaseStatusController.close();
  }
}
