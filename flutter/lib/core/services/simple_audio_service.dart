import 'dart:async';

import 'package:just_audio/just_audio.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/shared/constants/api_constants.dart';

/// Упрощенный глобальный аудио-сервис без SQLite зависимостей
class SimpleAudioService with LoggerMixin {
  static SimpleAudioService? _instance;
  static SimpleAudioService get instance =>
      _instance ??= SimpleAudioService._();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final CacheService _cacheService = CacheService.instance;

  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  Timer? _positionSaveTimer;

  // Callbacks для обновления UI
  final StreamController<SimpleAudioState> _stateController =
      StreamController<SimpleAudioState>.broadcast();
  Stream<SimpleAudioState> get stateStream => _stateController.stream;

  SimpleAudioState _currentState = const SimpleAudioState();
  SimpleAudioState get currentState => _currentState;

  SimpleAudioService._() {
    _initializeAudioPlayer();
  }

  /// Инициализация аудио плеера
  void _initializeAudioPlayer() {
    try {
      // Подписываемся на изменения состояния плеера
      _playerStateSubscription = _audioPlayer.playerStateStream.listen(
        (playerState) {
          try {
            final isPlaying = playerState.playing;
            final processingState = playerState.processingState;

            logInfo(
              '🎵 SimpleAudioService: Состояние плеера - playing: $isPlaying, processingState: $processingState',
            );

            _updateState(
              _currentState.copyWith(
                isPlaying: isPlaying,
                isLoading:
                    processingState == ProcessingState.loading ||
                    processingState == ProcessingState.buffering,
                isCompleted: processingState == ProcessingState.completed,
              ),
            );
          } catch (e) {
            logError('Ошибка при обработке состояния плеера', e);
          }
        },
        onError: (error) {
          logError('Ошибка в playerStateStream', error);
        },
      );

      // Подписываемся на изменения позиции
      _positionSubscription = _audioPlayer.positionStream.listen(
        (position) {
          try {
            _updateState(_currentState.copyWith(position: position));
            _schedulePositionSave();
          } catch (e) {
            logError('Ошибка при обработке позиции', e);
          }
        },
        onError: (error) {
          logError('Ошибка в positionStream', error);
        },
      );

      // Подписываемся на изменения длительности
      _durationSubscription = _audioPlayer.durationStream.listen(
        (duration) {
          try {
            if (duration != null) {
              logInfo(
                '🎵 SimpleAudioService: Длительность установлена - ${duration.inSeconds}s',
              );
              _updateState(_currentState.copyWith(duration: duration));
            }
          } catch (e) {
            logError('Ошибка при обработке длительности', e);
          }
        },
        onError: (error) {
          logError('Ошибка в durationStream', error);
        },
      );
    } catch (e) {
      logError('Критическая ошибка при инициализации аудио плеера', e);
    }
  }

  /// Обновление состояния
  void _updateState(SimpleAudioState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }

  /// Воспроизведение аудио
  Future<void> playAudio({
    required String fileId,
    required String? audioUrl,
    required String? sourcePageId,
    required String? sourcePageTitle,
    bool forceRefresh = false,
  }) async {
    try {
      final isSame = _currentState.currentFileId == fileId && !forceRefresh;

      if (isSame) {
        await _audioPlayer.play();
        return;
      }

      await _playFile(fileId, audioUrl, sourcePageId, sourcePageTitle);
      await _audioPlayer.play();
    } catch (e) {
      logError('Ошибка при воспроизведении аудио файла $fileId: $e');
    }
  }

  /// Внутренний метод запуска файла
  Future<void> _playFile(
    String fileId,
    String? audioUrl,
    String? sourcePageId,
    String? sourcePageTitle,
  ) async {
    if (audioUrl == null || audioUrl.isEmpty) {
      _updateState(_currentState.copyWith(isPlaying: false, showPlayer: false));
      return;
    }

    // Получаем сохранённую позицию
    int? savedSeconds = _cacheService.getAudioPosition(fileId);
    Duration startPosition =
        savedSeconds != null ? Duration(seconds: savedSeconds) : Duration.zero;

    _updateState(
      _currentState.copyWith(
        currentFileId: fileId,
        audioUrl: audioUrl,
        sourcePageId: sourcePageId,
        sourcePageTitle: sourcePageTitle,
        position: startPosition,
        duration: Duration.zero,
        showPlayer: true,
        isMinimized: false,
      ),
    );

    try {
      final fullAudioUrl = _getFullAudioUrl(audioUrl);
      await _audioPlayer.setUrl(fullAudioUrl);

      // Ждем получения длительности
      await Future.delayed(const Duration(milliseconds: 200));
      final duration = _audioPlayer.duration ?? Duration.zero;

      // Валидируем сохраненную позицию
      if (startPosition > duration && duration > Duration.zero) {
        startPosition = Duration.zero;
        try {
          await _cacheService.saveAudioPosition(fileId, 0);
        } catch (e) {
          logError('Ошибка при сбросе позиции аудио: $e');
        }
      }

      if (startPosition > Duration.zero && duration > Duration.zero) {
        await _audioPlayer.seek(startPosition);
      }
    } catch (e) {
      _updateState(_currentState.copyWith(isPlaying: false, showPlayer: false));
      logError('Ошибка при запуске аудио файла $fileId: $e');
    }
  }

  /// Получение полного URL
  String _getFullAudioUrl(String audioUrl) {
    if (audioUrl.startsWith('http')) {
      return audioUrl;
    }
    return '${ApiConstants.audioFilesUrl}$audioUrl';
  }

  /// Пауза аудио
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  /// Остановка аудио
  Future<void> stop() async {
    await _audioPlayer.stop();
    _updateState(const SimpleAudioState());
  }

  /// Поиск позиции
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// Перемотка назад
  Future<void> rewind() async {
    final newPosition = Duration(
      seconds: (_currentState.position.inSeconds - 10).clamp(
        0,
        _currentState.duration.inSeconds,
      ),
    );
    await _audioPlayer.seek(newPosition);
  }

  /// Перемотка вперед
  Future<void> fastForward() async {
    final newPosition = Duration(
      seconds: (_currentState.position.inSeconds + 10).clamp(
        0,
        _currentState.duration.inSeconds,
      ),
    );
    await _audioPlayer.seek(newPosition);
  }

  /// Минимизация плеера
  void minimizePlayer() {
    _updateState(_currentState.copyWith(isMinimized: true));
  }

  /// Максимизация плеера
  void maximizePlayer() {
    _updateState(_currentState.copyWith(isMinimized: false));
  }

  /// Планирование сохранения позиции
  void _schedulePositionSave() {
    _positionSaveTimer?.cancel();
    _positionSaveTimer = Timer(const Duration(seconds: 1), () {
      _saveCurrentPosition();
    });
  }

  /// Сохранение текущей позиции
  void _saveCurrentPosition() {
    if (_currentState.currentFileId != null) {
      try {
        _cacheService.saveAudioPosition(
          _currentState.currentFileId!,
          _currentState.position.inSeconds,
        );
      } catch (e) {
        logError('Ошибка при сохранении позиции аудио: $e');
      }
    }
  }

  /// Освобождение ресурсов
  Future<void> dispose() async {
    _positionSaveTimer?.cancel();
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();

    // Сохраняем позицию перед закрытием
    if (_currentState.currentFileId != null) {
      try {
        _cacheService.saveAudioPosition(
          _currentState.currentFileId!,
          _currentState.position.inSeconds,
        );
      } catch (e) {
        logError('Ошибка при сохранении позиции аудио: $e');
      }
    }

    await _audioPlayer.dispose();
    await _stateController.close();
  }
}

/// Состояние упрощенного аудио-сервиса
class SimpleAudioState {
  final String? currentFileId;
  final String? audioUrl;
  final String? sourcePageId;
  final String? sourcePageTitle;
  final bool isPlaying;
  final bool isLoading;
  final bool isCompleted;
  final Duration position;
  final Duration duration;
  final bool showPlayer;
  final bool isMinimized;
  final String? error;

  const SimpleAudioState({
    this.currentFileId,
    this.audioUrl,
    this.sourcePageId,
    this.sourcePageTitle,
    this.isPlaying = false,
    this.isLoading = false,
    this.isCompleted = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.showPlayer = false,
    this.isMinimized = false,
    this.error,
  });

  SimpleAudioState copyWith({
    String? currentFileId,
    String? audioUrl,
    String? sourcePageId,
    String? sourcePageTitle,
    bool? isPlaying,
    bool? isLoading,
    bool? isCompleted,
    Duration? position,
    Duration? duration,
    bool? showPlayer,
    bool? isMinimized,
    String? error,
  }) {
    return SimpleAudioState(
      currentFileId: currentFileId ?? this.currentFileId,
      audioUrl: audioUrl ?? this.audioUrl,
      sourcePageId: sourcePageId ?? this.sourcePageId,
      sourcePageTitle: sourcePageTitle ?? this.sourcePageTitle,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      isCompleted: isCompleted ?? this.isCompleted,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      showPlayer: showPlayer ?? this.showPlayer,
      isMinimized: isMinimized ?? this.isMinimized,
      error: error ?? this.error,
    );
  }
}
